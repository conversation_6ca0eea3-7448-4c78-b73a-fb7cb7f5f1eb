
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-应用模块", "02-应用模块", "{6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-架构核心", "01-架构核心", "{70DAC20A-**************-CF9412510099}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-应用服务", "03-应用服务", "{710F8A16-EABB-4119-B1D9-B376947DDF2C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Core", "01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj", "{CAD9CE4D-652B-405C-A404-12FF6EFCA56F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform", "03-应用服务\IotPlatform\IotPlatform.csproj", "{A990EECA-1DA9-4891-9CE4-8399510D44EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Web.Core", "03-应用服务\IotPlatform.Web.Core\IotPlatform.Web.Core.csproj", "{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "00-Common", "00-Common", "{A5FC8653-8709-4156-900C-AF8A78551823}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Common.Core", "02-应用模块\00-Common\Common.Core\Common.Core.csproj", "{4B782230-A18F-4261-96D3-E06C9C0F97E0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-OAuth", "01-OAuth", "{DCFD480E-776A-4576-A341-1B9ACB596FA7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OAuth", "02-应用模块\01-OAuth\OAuth\OAuth.csproj", "{5EF76D17-32C8-4630-82D5-0D0370679050}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-System", "02-System", "{36A99AD7-4FB8-49CE-8526-B5DFA18EB03F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Systems.Entity", "02-应用模块\02-System\Systems.Entity\Systems.Entity.csproj", "{0A256D9B-E8CA-4932-A290-1D11634D0A85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Common", "02-应用模块\00-Common\Common\Common.csproj", "{6398D3EE-A2F6-4882-8906-85F14C0F05DE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Extras.DatabaseAccessor.SqlSugar", "01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj", "{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Systems.Core", "02-应用模块\02-System\Systems.Core\Systems.Core.csproj", "{CE007D00-4A67-4897-9C66-E30FB4D3D5B2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Extras.Thridparty", "01-架构核心\Extras.Thridparty\Extras.Thridparty.csproj", "{D0B302F9-038C-47E8-B126-732BC328DD15}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-BusApp", "03-BusApp", "{6C71710B-34D5-4666-8B38-B10CB2F51DA4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-DataWeaving", "04-DataWeaving", "{D6567935-0538-4929-BF79-BCF413B1D203}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05-Message", "05-Message", "{EAF073DB-DFF6-4346-A014-EEF8051949B9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "06-Task", "06-Task", "{69260077-8946-48E6-85C6-3CD6C1730D97}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "07-Thing", "07-Thing", "{4600936B-C0B6-457F-B2E1-A65FB961E672}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "08-VisualData", "08-VisualData", "{0E9E03B5-ADB9-4B28-A5EA-6CB91F8A1938}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "09-Engine", "09-Engine", "{FF924973-AF6B-4DBB-80C3-8F0232E8DDE0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Application", "02-应用模块\03-BusApp\IotPlatform.Application\IotPlatform.Application.csproj", "{67948704-7C34-4247-8D18-E4D1D9E1118E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.DataWeaving", "02-应用模块\04-DataWeaving\IotPlatform.DataWeaving\IotPlatform.DataWeaving.csproj", "{47421740-5181-4E70-BA2F-F376FE1E2E57}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.PolicyMessage", "02-应用模块\05-Message\IotPlatform.PolicyMessage\IotPlatform.PolicyMessage.csproj", "{B465AD7A-B083-45E2-84C3-E39A637307BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.VisualData", "02-应用模块\08-VisualData\IotPlatform.VisualData\IotPlatform.VisualData.csproj", "{01F7E848-A228-47DA-87D3-F6B9F0C9A20E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "10-VisualDev", "10-VisualDev", "{616F524F-DFA3-4DC6-BEC5-8AC46A11F82D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualDev", "02-应用模块\10-VisualDev\VisualDev\VisualDev.csproj", "{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualDev.Entity", "02-应用模块\10-VisualDev\VisualDev.Entity\VisualDev.Entity.csproj", "{B7D9BA89-1166-418B-9B7D-A9845D022E6E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualDev.Interface", "02-应用模块\10-VisualDev\VisualDev.Interface\VisualDev.Interface.csproj", "{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualDev.Engine", "02-应用模块\09-Engine\VisualDev.Engine\VisualDev.Engine.csproj", "{1BB62AA7-1FE6-4690-9420-BB751F68C8D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Engine.Entity", "02-应用模块\09-Engine\Engine.Entity\Engine.Entity.csproj", "{936ABA61-5437-48BC-98C1-3D49E2285BF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Systems.Interface", "02-应用模块\02-System\Systems.Interface\Systems.Interface.csproj", "{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JsScript.Engine", "02-应用模块\09-Engine\JsScript.Engine\JsScript.Engine.csproj", "{C81E6D24-F7FE-45A2-9FE4-B84B2968D428}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mqtt.Engine", "02-应用模块\09-Engine\Mqtt.Engine\Mqtt.Engine.csproj", "{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Extras.TDengine", "01-架构核心\Extras.TDengine\Extras.TDengine.csproj", "{CABE9B12-A330-4A2C-AAD6-90908A227F9B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "00-Model", "00-Model", "{55B1D9B2-C335-4BAE-98A9-93E08E831CEB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-Warning", "01-Warning", "{B7AC608C-E897-4B65-8AC7-F8F339C2C9F4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.Entity", "02-应用模块\07-Thing\00-Model\IotPlatform.Thing.Entity\IotPlatform.Thing.Entity.csproj", "{2E4338B0-A2B2-4D14-812A-AB396F24ED76}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.Warning.Entity", "02-应用模块\07-Thing\01-Warning\IotPlatform.Thing.Warning.Entity\IotPlatform.Thing.Warning.Entity.csproj", "{DB002319-0FCE-4470-B840-65002C424B66}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.Warning", "02-应用模块\07-Thing\01-Warning\IotPlatform.Thing.Warning\IotPlatform.Thing.Warning.csproj", "{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-StatisticalRule", "02-StatisticalRule", "{348DD03F-41C3-4581-A576-B7B84226AF70}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.StatisticalRule.Entity", "02-应用模块\07-Thing\02-StatisticalRule\IotPlatform.Thing.StatisticalRule.Entity\IotPlatform.Thing.StatisticalRule.Entity.csproj", "{5D205C41-30B9-45FB-B4D6-A580F41F516A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.StatisticalRule", "02-应用模块\07-Thing\02-StatisticalRule\IotPlatform.Thing.StatisticalRule\IotPlatform.Thing.StatisticalRule.csproj", "{52082240-1106-4A6B-A31D-F3BF30DD0748}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-ProgramBlock", "02-ProgramBlock", "{4EF5FB53-211B-4EEC-8E0F-1808716D5DC0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "00-Task", "00-Task", "{7653D952-E1B2-487F-B925-7039D26A1707}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-WorkFlow", "01-WorkFlow", "{FB88F64E-91B3-4942-9333-A12F903C2F7C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.WorkFlow.Entity", "02-应用模块\06-Task\01-WorkFlow\IotPlatform.WorkFlow.Entity\IotPlatform.WorkFlow.Entity.csproj", "{128F07B2-E466-44A4-8276-E91C0D730C47}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.WorkFlow", "02-应用模块\06-Task\01-WorkFlow\IotPlatform.WorkFlow\IotPlatform.WorkFlow.csproj", "{109C61EE-5E13-42B6-AE4E-A56E4791D46D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.ProgramBlock.Entity", "02-应用模块\06-Task\02-ProgramBlock\IotPlatform.ProgramBlock.Entity\IotPlatform.ProgramBlock.Entity.csproj", "{D041F255-9654-4DFD-B7B1-F193DDA4FA3F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.ProgramBlock", "02-应用模块\06-Task\02-ProgramBlock\IotPlatform.ProgramBlock\IotPlatform.ProgramBlock.csproj", "{C759A790-367C-4791-ABB3-C0367735256C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Task.Entity", "02-应用模块\06-Task\00-Task\IotPlatform.Task.Entity\IotPlatform.Task.Entity.csproj", "{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Task", "02-应用模块\06-Task\00-Task\IotPlatform.Task\IotPlatform.Task.csproj", "{FB6636CC-5B44-4F35-AA36-E852282D92C2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-RemoteControl", "03-RemoteControl", "{ACCC44A4-F094-4E58-B898-188CF0CFA53F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.RemoteControl", "02-应用模块\07-Thing\03-RemoteControl\IotPlatform.Thing.RemoteControl\IotPlatform.Thing.RemoteControl.csproj", "{E2ABE0C5-6727-4663-A497-7B78706FBA69}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Thing.RemoteControl.Entity", "02-应用模块\07-Thing\03-RemoteControl\IotPlatform.Thing.RemoteControl.Entity\IotPlatform.Thing.RemoteControl.Entity.csproj", "{AA26AFAC-3F05-4395-B211-3F51E6633649}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "11-Extend", "11-Extend", "{B63C45C3-C15F-447A-A073-58B468B7E724}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Extend", "02-应用模块\11-Extend\IotPlatform.Extend\IotPlatform.Extend.csproj", "{73243FE1-965B-46C5-B7AD-C458EA08F2FB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Extend.Entity", "02-应用模块\11-Extend\IotPlatform.Extend.Entity\IotPlatform.Extend.Entity.csproj", "{6B46266C-49E7-48C9-82AD-4E4B309D4BE6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-VideoDevice", "04-VideoDevice", "{C0DEE583-6463-4342-AE26-2D64138440DD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.ThingModel", "02-应用模块\07-Thing\00-Model\IotPlatform.ThingModel\IotPlatform.ThingModel.csproj", "{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Video", "02-应用模块\07-Thing\04-VideoDevice\IotPlatform.Video\IotPlatform.Video.csproj", "{B3111CF1-9745-44AB-87B8-5924891A02FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.Video.Entity", "02-应用模块\07-Thing\04-VideoDevice\IotPlatform.Video.Entity\IotPlatform.Video.Entity.csproj", "{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SharpRTSPtoWebRTC", "02-应用模块\07-Thing\04-VideoDevice\SharpRTSPtoWebRTC\SharpRTSPtoWebRTC.csproj", "{4B5CC6DC-8F2F-4288-8409-09DF667446EB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Extras.MQTT", "01-架构核心\Extras.MQTT\Extras.MQTT.csproj", "{05F0A81E-61ED-4D03-885D-7A1EC5781217}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-Test", "04-Test", "{09E493AC-5A88-48AB-B2DC-D0E66FC8C746}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SocketTest", "04-Test\SocketTest\SocketTest.csproj", "{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{C4B908F9-937E-473E-9B76-DE8FD6A7EBCC}"
	ProjectSection(SolutionItems) = preProject
		compose.yaml = compose.yaml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotPlatform.DongleLicense", "02-应用模块\11-Extend\IotPlatform.DongleLicense\IotPlatform.DongleLicense.csproj", "{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CAD9CE4D-652B-405C-A404-12FF6EFCA56F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CAD9CE4D-652B-405C-A404-12FF6EFCA56F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CAD9CE4D-652B-405C-A404-12FF6EFCA56F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CAD9CE4D-652B-405C-A404-12FF6EFCA56F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A990EECA-1DA9-4891-9CE4-8399510D44EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A990EECA-1DA9-4891-9CE4-8399510D44EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A990EECA-1DA9-4891-9CE4-8399510D44EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A990EECA-1DA9-4891-9CE4-8399510D44EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B782230-A18F-4261-96D3-E06C9C0F97E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B782230-A18F-4261-96D3-E06C9C0F97E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B782230-A18F-4261-96D3-E06C9C0F97E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B782230-A18F-4261-96D3-E06C9C0F97E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{5EF76D17-32C8-4630-82D5-0D0370679050}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5EF76D17-32C8-4630-82D5-0D0370679050}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5EF76D17-32C8-4630-82D5-0D0370679050}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5EF76D17-32C8-4630-82D5-0D0370679050}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A256D9B-E8CA-4932-A290-1D11634D0A85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A256D9B-E8CA-4932-A290-1D11634D0A85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A256D9B-E8CA-4932-A290-1D11634D0A85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A256D9B-E8CA-4932-A290-1D11634D0A85}.Release|Any CPU.Build.0 = Release|Any CPU
		{6398D3EE-A2F6-4882-8906-85F14C0F05DE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6398D3EE-A2F6-4882-8906-85F14C0F05DE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6398D3EE-A2F6-4882-8906-85F14C0F05DE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6398D3EE-A2F6-4882-8906-85F14C0F05DE}.Release|Any CPU.Build.0 = Release|Any CPU
		{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE007D00-4A67-4897-9C66-E30FB4D3D5B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE007D00-4A67-4897-9C66-E30FB4D3D5B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE007D00-4A67-4897-9C66-E30FB4D3D5B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE007D00-4A67-4897-9C66-E30FB4D3D5B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0B302F9-038C-47E8-B126-732BC328DD15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0B302F9-038C-47E8-B126-732BC328DD15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0B302F9-038C-47E8-B126-732BC328DD15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0B302F9-038C-47E8-B126-732BC328DD15}.Release|Any CPU.Build.0 = Release|Any CPU
		{67948704-7C34-4247-8D18-E4D1D9E1118E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67948704-7C34-4247-8D18-E4D1D9E1118E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67948704-7C34-4247-8D18-E4D1D9E1118E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67948704-7C34-4247-8D18-E4D1D9E1118E}.Release|Any CPU.Build.0 = Release|Any CPU
		{47421740-5181-4E70-BA2F-F376FE1E2E57}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47421740-5181-4E70-BA2F-F376FE1E2E57}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47421740-5181-4E70-BA2F-F376FE1E2E57}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47421740-5181-4E70-BA2F-F376FE1E2E57}.Release|Any CPU.Build.0 = Release|Any CPU
		{B465AD7A-B083-45E2-84C3-E39A637307BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B465AD7A-B083-45E2-84C3-E39A637307BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B465AD7A-B083-45E2-84C3-E39A637307BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B465AD7A-B083-45E2-84C3-E39A637307BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{01F7E848-A228-47DA-87D3-F6B9F0C9A20E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01F7E848-A228-47DA-87D3-F6B9F0C9A20E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01F7E848-A228-47DA-87D3-F6B9F0C9A20E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01F7E848-A228-47DA-87D3-F6B9F0C9A20E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7D9BA89-1166-418B-9B7D-A9845D022E6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7D9BA89-1166-418B-9B7D-A9845D022E6E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7D9BA89-1166-418B-9B7D-A9845D022E6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7D9BA89-1166-418B-9B7D-A9845D022E6E}.Release|Any CPU.Build.0 = Release|Any CPU
		{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BB62AA7-1FE6-4690-9420-BB751F68C8D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BB62AA7-1FE6-4690-9420-BB751F68C8D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BB62AA7-1FE6-4690-9420-BB751F68C8D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BB62AA7-1FE6-4690-9420-BB751F68C8D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{936ABA61-5437-48BC-98C1-3D49E2285BF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{936ABA61-5437-48BC-98C1-3D49E2285BF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{936ABA61-5437-48BC-98C1-3D49E2285BF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{936ABA61-5437-48BC-98C1-3D49E2285BF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506}.Release|Any CPU.Build.0 = Release|Any CPU
		{C81E6D24-F7FE-45A2-9FE4-B84B2968D428}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C81E6D24-F7FE-45A2-9FE4-B84B2968D428}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C81E6D24-F7FE-45A2-9FE4-B84B2968D428}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C81E6D24-F7FE-45A2-9FE4-B84B2968D428}.Release|Any CPU.Build.0 = Release|Any CPU
		{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{CABE9B12-A330-4A2C-AAD6-90908A227F9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CABE9B12-A330-4A2C-AAD6-90908A227F9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CABE9B12-A330-4A2C-AAD6-90908A227F9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CABE9B12-A330-4A2C-AAD6-90908A227F9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E4338B0-A2B2-4D14-812A-AB396F24ED76}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E4338B0-A2B2-4D14-812A-AB396F24ED76}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E4338B0-A2B2-4D14-812A-AB396F24ED76}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E4338B0-A2B2-4D14-812A-AB396F24ED76}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB002319-0FCE-4470-B840-65002C424B66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB002319-0FCE-4470-B840-65002C424B66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB002319-0FCE-4470-B840-65002C424B66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB002319-0FCE-4470-B840-65002C424B66}.Release|Any CPU.Build.0 = Release|Any CPU
		{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D205C41-30B9-45FB-B4D6-A580F41F516A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D205C41-30B9-45FB-B4D6-A580F41F516A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D205C41-30B9-45FB-B4D6-A580F41F516A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D205C41-30B9-45FB-B4D6-A580F41F516A}.Release|Any CPU.Build.0 = Release|Any CPU
		{52082240-1106-4A6B-A31D-F3BF30DD0748}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52082240-1106-4A6B-A31D-F3BF30DD0748}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52082240-1106-4A6B-A31D-F3BF30DD0748}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52082240-1106-4A6B-A31D-F3BF30DD0748}.Release|Any CPU.Build.0 = Release|Any CPU
		{128F07B2-E466-44A4-8276-E91C0D730C47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{128F07B2-E466-44A4-8276-E91C0D730C47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{128F07B2-E466-44A4-8276-E91C0D730C47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{128F07B2-E466-44A4-8276-E91C0D730C47}.Release|Any CPU.Build.0 = Release|Any CPU
		{109C61EE-5E13-42B6-AE4E-A56E4791D46D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{109C61EE-5E13-42B6-AE4E-A56E4791D46D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{109C61EE-5E13-42B6-AE4E-A56E4791D46D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{109C61EE-5E13-42B6-AE4E-A56E4791D46D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D041F255-9654-4DFD-B7B1-F193DDA4FA3F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D041F255-9654-4DFD-B7B1-F193DDA4FA3F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D041F255-9654-4DFD-B7B1-F193DDA4FA3F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D041F255-9654-4DFD-B7B1-F193DDA4FA3F}.Release|Any CPU.Build.0 = Release|Any CPU
		{C759A790-367C-4791-ABB3-C0367735256C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C759A790-367C-4791-ABB3-C0367735256C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C759A790-367C-4791-ABB3-C0367735256C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C759A790-367C-4791-ABB3-C0367735256C}.Release|Any CPU.Build.0 = Release|Any CPU
		{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB6636CC-5B44-4F35-AA36-E852282D92C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB6636CC-5B44-4F35-AA36-E852282D92C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB6636CC-5B44-4F35-AA36-E852282D92C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB6636CC-5B44-4F35-AA36-E852282D92C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{E2ABE0C5-6727-4663-A497-7B78706FBA69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E2ABE0C5-6727-4663-A497-7B78706FBA69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E2ABE0C5-6727-4663-A497-7B78706FBA69}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E2ABE0C5-6727-4663-A497-7B78706FBA69}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA26AFAC-3F05-4395-B211-3F51E6633649}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA26AFAC-3F05-4395-B211-3F51E6633649}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA26AFAC-3F05-4395-B211-3F51E6633649}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA26AFAC-3F05-4395-B211-3F51E6633649}.Release|Any CPU.Build.0 = Release|Any CPU
		{73243FE1-965B-46C5-B7AD-C458EA08F2FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73243FE1-965B-46C5-B7AD-C458EA08F2FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73243FE1-965B-46C5-B7AD-C458EA08F2FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73243FE1-965B-46C5-B7AD-C458EA08F2FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B46266C-49E7-48C9-82AD-4E4B309D4BE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B46266C-49E7-48C9-82AD-4E4B309D4BE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B46266C-49E7-48C9-82AD-4E4B309D4BE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B46266C-49E7-48C9-82AD-4E4B309D4BE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3111CF1-9745-44AB-87B8-5924891A02FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3111CF1-9745-44AB-87B8-5924891A02FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3111CF1-9745-44AB-87B8-5924891A02FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3111CF1-9745-44AB-87B8-5924891A02FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B5CC6DC-8F2F-4288-8409-09DF667446EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B5CC6DC-8F2F-4288-8409-09DF667446EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B5CC6DC-8F2F-4288-8409-09DF667446EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B5CC6DC-8F2F-4288-8409-09DF667446EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{05F0A81E-61ED-4D03-885D-7A1EC5781217}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05F0A81E-61ED-4D03-885D-7A1EC5781217}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05F0A81E-61ED-4D03-885D-7A1EC5781217}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05F0A81E-61ED-4D03-885D-7A1EC5781217}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CAD9CE4D-652B-405C-A404-12FF6EFCA56F} = {70DAC20A-**************-CF9412510099}
		{A990EECA-1DA9-4891-9CE4-8399510D44EE} = {710F8A16-EABB-4119-B1D9-B376947DDF2C}
		{BD6E51D2-4EA4-4AB2-8C08-688C18ECF6BC} = {710F8A16-EABB-4119-B1D9-B376947DDF2C}
		{A5FC8653-8709-4156-900C-AF8A78551823} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{4B782230-A18F-4261-96D3-E06C9C0F97E0} = {A5FC8653-8709-4156-900C-AF8A78551823}
		{DCFD480E-776A-4576-A341-1B9ACB596FA7} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{5EF76D17-32C8-4630-82D5-0D0370679050} = {DCFD480E-776A-4576-A341-1B9ACB596FA7}
		{36A99AD7-4FB8-49CE-8526-B5DFA18EB03F} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{0A256D9B-E8CA-4932-A290-1D11634D0A85} = {36A99AD7-4FB8-49CE-8526-B5DFA18EB03F}
		{6398D3EE-A2F6-4882-8906-85F14C0F05DE} = {A5FC8653-8709-4156-900C-AF8A78551823}
		{10D8D464-2DDF-473A-AE34-A4FE6D0D2E11} = {70DAC20A-**************-CF9412510099}
		{CE007D00-4A67-4897-9C66-E30FB4D3D5B2} = {36A99AD7-4FB8-49CE-8526-B5DFA18EB03F}
		{D0B302F9-038C-47E8-B126-732BC328DD15} = {70DAC20A-**************-CF9412510099}
		{6C71710B-34D5-4666-8B38-B10CB2F51DA4} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{D6567935-0538-4929-BF79-BCF413B1D203} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{EAF073DB-DFF6-4346-A014-EEF8051949B9} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{69260077-8946-48E6-85C6-3CD6C1730D97} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{4600936B-C0B6-457F-B2E1-A65FB961E672} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{0E9E03B5-ADB9-4B28-A5EA-6CB91F8A1938} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{FF924973-AF6B-4DBB-80C3-8F0232E8DDE0} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{67948704-7C34-4247-8D18-E4D1D9E1118E} = {6C71710B-34D5-4666-8B38-B10CB2F51DA4}
		{47421740-5181-4E70-BA2F-F376FE1E2E57} = {D6567935-0538-4929-BF79-BCF413B1D203}
		{B465AD7A-B083-45E2-84C3-E39A637307BB} = {EAF073DB-DFF6-4346-A014-EEF8051949B9}
		{01F7E848-A228-47DA-87D3-F6B9F0C9A20E} = {0E9E03B5-ADB9-4B28-A5EA-6CB91F8A1938}
		{616F524F-DFA3-4DC6-BEC5-8AC46A11F82D} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{3FBA8747-BF7D-4EE1-B74F-D780F8A2F623} = {616F524F-DFA3-4DC6-BEC5-8AC46A11F82D}
		{B7D9BA89-1166-418B-9B7D-A9845D022E6E} = {616F524F-DFA3-4DC6-BEC5-8AC46A11F82D}
		{28B063D5-2B4A-4208-B7A0-1776DCB8C2BF} = {616F524F-DFA3-4DC6-BEC5-8AC46A11F82D}
		{1BB62AA7-1FE6-4690-9420-BB751F68C8D1} = {FF924973-AF6B-4DBB-80C3-8F0232E8DDE0}
		{936ABA61-5437-48BC-98C1-3D49E2285BF2} = {FF924973-AF6B-4DBB-80C3-8F0232E8DDE0}
		{F11EDF26-C3EB-4FF6-9ABC-D019C7FB7506} = {36A99AD7-4FB8-49CE-8526-B5DFA18EB03F}
		{C81E6D24-F7FE-45A2-9FE4-B84B2968D428} = {FF924973-AF6B-4DBB-80C3-8F0232E8DDE0}
		{C59C19C1-A1E9-449B-B1B7-3BDB2D2755AD} = {FF924973-AF6B-4DBB-80C3-8F0232E8DDE0}
		{CABE9B12-A330-4A2C-AAD6-90908A227F9B} = {70DAC20A-**************-CF9412510099}
		{55B1D9B2-C335-4BAE-98A9-93E08E831CEB} = {4600936B-C0B6-457F-B2E1-A65FB961E672}
		{B7AC608C-E897-4B65-8AC7-F8F339C2C9F4} = {4600936B-C0B6-457F-B2E1-A65FB961E672}
		{2E4338B0-A2B2-4D14-812A-AB396F24ED76} = {55B1D9B2-C335-4BAE-98A9-93E08E831CEB}
		{DB002319-0FCE-4470-B840-65002C424B66} = {B7AC608C-E897-4B65-8AC7-F8F339C2C9F4}
		{9CC62DDF-4296-4FF2-B42F-3EA4CA7743EA} = {B7AC608C-E897-4B65-8AC7-F8F339C2C9F4}
		{348DD03F-41C3-4581-A576-B7B84226AF70} = {4600936B-C0B6-457F-B2E1-A65FB961E672}
		{5D205C41-30B9-45FB-B4D6-A580F41F516A} = {348DD03F-41C3-4581-A576-B7B84226AF70}
		{52082240-1106-4A6B-A31D-F3BF30DD0748} = {348DD03F-41C3-4581-A576-B7B84226AF70}
		{4EF5FB53-211B-4EEC-8E0F-1808716D5DC0} = {69260077-8946-48E6-85C6-3CD6C1730D97}
		{7653D952-E1B2-487F-B925-7039D26A1707} = {69260077-8946-48E6-85C6-3CD6C1730D97}
		{FB88F64E-91B3-4942-9333-A12F903C2F7C} = {69260077-8946-48E6-85C6-3CD6C1730D97}
		{128F07B2-E466-44A4-8276-E91C0D730C47} = {FB88F64E-91B3-4942-9333-A12F903C2F7C}
		{109C61EE-5E13-42B6-AE4E-A56E4791D46D} = {FB88F64E-91B3-4942-9333-A12F903C2F7C}
		{D041F255-9654-4DFD-B7B1-F193DDA4FA3F} = {4EF5FB53-211B-4EEC-8E0F-1808716D5DC0}
		{C759A790-367C-4791-ABB3-C0367735256C} = {4EF5FB53-211B-4EEC-8E0F-1808716D5DC0}
		{10E89EB9-D9FB-4904-85EC-AC8A1A8D15E2} = {7653D952-E1B2-487F-B925-7039D26A1707}
		{FB6636CC-5B44-4F35-AA36-E852282D92C2} = {7653D952-E1B2-487F-B925-7039D26A1707}
		{ACCC44A4-F094-4E58-B898-188CF0CFA53F} = {4600936B-C0B6-457F-B2E1-A65FB961E672}
		{E2ABE0C5-6727-4663-A497-7B78706FBA69} = {ACCC44A4-F094-4E58-B898-188CF0CFA53F}
		{AA26AFAC-3F05-4395-B211-3F51E6633649} = {ACCC44A4-F094-4E58-B898-188CF0CFA53F}
		{B63C45C3-C15F-447A-A073-58B468B7E724} = {6EE1B84B-F8BE-4BFA-8AB6-23A177C92E0D}
		{73243FE1-965B-46C5-B7AD-C458EA08F2FB} = {B63C45C3-C15F-447A-A073-58B468B7E724}
		{6B46266C-49E7-48C9-82AD-4E4B309D4BE6} = {B63C45C3-C15F-447A-A073-58B468B7E724}
		{C0DEE583-6463-4342-AE26-2D64138440DD} = {4600936B-C0B6-457F-B2E1-A65FB961E672}
		{92073D82-CF43-49CE-A6BE-4C0F52CEDE0C} = {55B1D9B2-C335-4BAE-98A9-93E08E831CEB}
		{B3111CF1-9745-44AB-87B8-5924891A02FF} = {C0DEE583-6463-4342-AE26-2D64138440DD}
		{FEEA1BC9-70F5-4ACE-8831-B6C13D9D0DC7} = {C0DEE583-6463-4342-AE26-2D64138440DD}
		{4B5CC6DC-8F2F-4288-8409-09DF667446EB} = {C0DEE583-6463-4342-AE26-2D64138440DD}
		{05F0A81E-61ED-4D03-885D-7A1EC5781217} = {70DAC20A-**************-CF9412510099}
		{D3B350BA-1A74-4E99-AF7C-EDAFB826F8A1} = {09E493AC-5A88-48AB-B2DC-D0E66FC8C746}
		{1E5DFCF9-CEAF-4B25-A2EF-67EE0FFDF776} = {B63C45C3-C15F-447A-A073-58B468B7E724}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5CD801D7-984A-4F5C-8FA2-211B7A5EA9F3}
	EndGlobalSection
EndGlobal
