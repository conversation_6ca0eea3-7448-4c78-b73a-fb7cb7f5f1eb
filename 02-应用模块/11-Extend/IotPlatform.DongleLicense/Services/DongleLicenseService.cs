using IotPlatform.DongleLicense.HostedService;
using IotPlatform.DongleLicense.Models;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁许可证管理服务
/// 版 本:V5.0.0
/// 版 权:杭州峰回科技有限公司
/// 作 者:系统生成
/// 日 期:2024-01-20
/// </summary>
[ApiDescriptionSettings("加密锁许可证")]
public class DongleLicenseService : IDynamicApiController, ITransient
{
    private readonly DongleLicenseHostedService _hostedService;
    private readonly DongleCheckService _checkService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="hostedService">后台托管服务</param>
    /// <param name="checkService">检查服务</param>
    public DongleLicenseService(
        DongleLicenseHostedService hostedService,
        DongleCheckService checkService)
    {
        _hostedService = hostedService;
        _checkService = checkService;
    }

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态信息</returns>
    [HttpGet("/dongleLicense/status")]
    public dynamic GetServiceStatus()
    {
        return _hostedService.GetServiceStatus();
    }

    /// <summary>
    /// 获取最后一次检查结果
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpGet("/dongleLicense/lastCheck")]
    public DongleCheckResult? GetLastCheckResult()
    {
        return _hostedService.LastCheckResult;
    }

    /// <summary>
    /// 手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpPost("/dongleLicense/triggerCheck")]
    public async Task<DongleCheckResult> TriggerCheck()
    {
        return await _hostedService.TriggerCheckAsync();
    }

    /// <summary>
    /// 获取加密锁详细信息
    /// </summary>
    /// <returns>详细信息</returns>
    [HttpGet("/dongleLicense/detailInfo")]
    public async Task<string> GetDetailInfo()
    {
        return await _checkService.GetDongleDetailInfoAsync();
    }

    /// <summary>
    /// 获取加密锁设备列表
    /// </summary>
    /// <returns>设备列表</returns>
    [HttpGet("/dongleLicense/devices")]
    public async Task<dynamic> GetDevices()
    {
        var checkResult = await _checkService.CheckDongleAsync();
        
        var devices = checkResult.DongleInfos.Select(d => new
        {
            d.Index,
            d.Version,
            d.ProductType,
            d.BirthDay,
            d.AgentId,
            d.ProductId,
            d.UserId,
            d.HardwareId,
            d.IsMother,
            d.DeviceType,
            d.CanOpenClose,
            d.OpenCloseError,
            Status = d.CanOpenClose ? "正常" : "异常"
        }).ToList();

        return new
        {
            checkTime = checkResult.CheckTime,
            isSuccess = checkResult.IsSuccess,
            deviceCount = checkResult.DongleCount,
            elapsedMs = checkResult.ElapsedMilliseconds,
            errorMessage = checkResult.ErrorMessage,
            devices
        };
    }

    /// <summary>
    /// 获取系统健康状态（包含加密锁状态）
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("/dongleLicense/health")]
    public dynamic GetHealth()
    {
        var lastResult = _hostedService.LastCheckResult;
        
        return new
        {
            service = new
            {
                isRunning = _hostedService.IsRunning,
                status = _hostedService.IsRunning ? "运行中" : "已停止"
            },
            lastCheck = lastResult != null ? new
            {
                time = lastResult.CheckTime,
                success = lastResult.IsSuccess,
                deviceCount = lastResult.DongleCount,
                message = lastResult.ErrorMessage
            } : null,
            overall = new
            {
                status = _hostedService.IsRunning && (lastResult?.IsSuccess ?? false) ? "健康" : "异常",
                timestamp = DateTime.Now
            }
        };
    }
}
