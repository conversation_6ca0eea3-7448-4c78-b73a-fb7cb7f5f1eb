# 加密锁许可证检查模块测试说明

## 测试前准备

### 1. 环境要求
- 确保加密锁硬件已正确连接到服务器
- 确保加密锁驱动程序已正确安装
- 确保 `Dongle_d.dll` 文件已放置在项目根目录

### 2. 配置检查
检查 `Configuration/DongleLicense.json` 配置文件：
```json
{
  "DongleLicense": {
    "CheckIntervalMinutes": 1,        // 测试时可设置为1分钟
    "EnableCheck": true,              // 确保启用检查
    "TestOpenClose": true,            // 启用打开关闭测试
    "OpenCloseTimeoutMs": 5000,       // 超时时间
    "EnableDetailedLogging": true,    // 启用详细日志
    "SendAlarmOnFailure": true        // 启用告警
  }
}
```

## 测试步骤

### 第一步：基本功能测试
使用Swagger或Postman调用基本测试接口：

```http
POST /api/dongleTest/basicTest
```

**预期结果**：
- 返回成功状态
- 显示设备详细信息
- 包含设备数量、版本、产品ID等信息

**示例响应**：
```json
{
  "message": "测试完成",
  "data": "=== 加密锁基本功能测试 ===\nEnum Dongle Success! Count: 1\n...",
  "deviceCount": 1
}
```

### 第二步：设备枚举测试
```http
GET /api/dongleTest/enumTest
```

**预期结果**：
- 返回设备数量和详细信息
- 包含硬件ID、产品类型等

### 第三步：设备打开关闭测试
```http
POST /api/dongleTest/openCloseTest?index=0
```

**预期结果**：
- 成功打开和关闭设备
- 返回设备句柄信息

### 第四步：服务状态检查
```http
GET /api/dongleLicense/status
```

**预期结果**：
- 显示服务运行状态
- 显示配置信息
- 显示最后检查结果

### 第五步：手动触发检查
```http
POST /api/dongleLicense/triggerCheck
```

**预期结果**：
- 立即执行一次完整检查
- 返回检查结果和设备信息

### 第六步：获取设备列表
```http
GET /api/dongleLicense/devices
```

**预期结果**：
- 返回所有检测到的设备列表
- 包含每个设备的状态信息

### 第七步：健康状态检查
```http
GET /api/dongleLicense/health
```

**预期结果**：
- 显示整体健康状态
- 包含服务状态和最后检查结果

## 日志检查

### 1. 启动日志
服务启动时应看到类似日志：
```
[Information] 加密锁许可证检查服务正在启动...
[Information] 检查间隔: 1 分钟
[Information] 加密锁许可证检查服务已启动
```

### 2. 检查日志
定时检查时应看到类似日志：
```
[Information] 开始执行加密锁检查...
[Information] 检测到 1 个加密锁设备
[Information] 加密锁检查成功 - 设备数量: 1, 耗时: 1234ms
[Debug] 设备 0: 标准版, PID: 12345678, 状态: 正常
```

### 3. 错误日志
如果出现问题，会看到类似日志：
```
[Error] 枚举加密锁失败，错误代码: XXXXXXXX
[Error] 设备 0 访问失败: 打开设备失败，错误代码: XXXXXXXX
[Warning] 【加密锁告警】检查失败 - 时间: 2024-01-20 10:00:00, 错误: 未检测到加密锁设备
```

## 常见问题排查

### 1. 枚举设备失败
**现象**：调用 `/api/dongleTest/enumTest` 返回错误
**可能原因**：
- 加密锁未连接或连接不良
- 驱动程序未正确安装
- DLL文件缺失或版本不匹配

**排查步骤**：
1. 检查硬件连接
2. 重新安装驱动程序
3. 确认DLL文件存在且版本正确

### 2. 打开设备失败
**现象**：设备枚举成功但打开失败
**可能原因**：
- 设备被其他程序占用
- 权限不足
- 设备故障

**排查步骤**：
1. 关闭其他可能使用加密锁的程序
2. 以管理员权限运行应用程序
3. 检查设备硬件状态

### 3. 服务未启动
**现象**：调用 `/api/dongleLicense/status` 显示服务未运行
**可能原因**：
- 配置中 `EnableCheck` 设置为 false
- 服务启动时发生异常

**排查步骤**：
1. 检查配置文件
2. 查看启动日志
3. 检查依赖项是否正确安装

### 4. 定时检查不工作
**现象**：手动检查正常，但定时检查不执行
**可能原因**：
- 检查间隔设置过长
- 服务被意外停止

**排查步骤**：
1. 调整检查间隔为较短时间（如1分钟）
2. 检查服务状态
3. 查看相关日志

## 性能测试

### 1. 响应时间测试
- 基本功能测试：应在5秒内完成
- 设备枚举：应在1秒内完成
- 设备打开关闭：应在3秒内完成

### 2. 并发测试
- 同时调用多个API接口
- 确保不会出现死锁或资源竞争

### 3. 长期运行测试
- 让服务运行24小时以上
- 观察内存使用情况
- 检查是否有内存泄漏

## 测试报告模板

```
测试时间：2024-01-20 10:00:00
测试环境：Windows Server 2019
加密锁型号：[填写具体型号]
DLL版本：[填写版本号]

测试结果：
□ 基本功能测试 - 通过/失败
□ 设备枚举测试 - 通过/失败  
□ 设备打开关闭测试 - 通过/失败
□ 服务状态检查 - 通过/失败
□ 手动触发检查 - 通过/失败
□ 设备列表获取 - 通过/失败
□ 健康状态检查 - 通过/失败

问题记录：
[记录测试过程中发现的问题]

建议：
[记录优化建议]
```
