using IotPlatform.DongleLicense.Models;
using IotPlatform.DongleLicense.Services;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace IotPlatform.DongleLicense.HostedService;

/// <summary>
/// 加密锁许可证检查后台服务
/// </summary>
public class DongleLicenseHostedService : IHostedService, IDisposable
{
    private readonly DongleCheckService _dongleCheckService;
    private readonly ILogger<DongleLicenseHostedService> _logger;
    private readonly DongleCheckConfig _config;
    private Timer? _timer;
    private bool _disposed = false;

    /// <summary>
    /// 最后一次检查结果
    /// </summary>
    public DongleCheckResult? LastCheckResult { get; private set; }

    /// <summary>
    /// 服务状态
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dongleCheckService">加密锁检查服务</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">配置选项</param>
    public DongleLicenseHostedService(
        DongleCheckService dongleCheckService,
        ILogger<DongleLicenseHostedService> logger,
        IOptions<DongleCheckConfig> config)
    {
        _dongleCheckService = dongleCheckService;
        _logger = logger;
        _config = config.Value;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        if (!_config.EnableCheck)
        {
            _logger.LogInformation("加密锁检查服务已禁用");
            return Task.CompletedTask;
        }

        _logger.LogInformation("加密锁许可证检查服务正在启动...");
        _logger.LogInformation("检查间隔: {IntervalMinutes} 分钟", _config.CheckIntervalMinutes);

        IsRunning = true;

        // 立即执行一次检查，然后按间隔定时执行
        _timer = new Timer(
            DoWork, 
            null, 
            TimeSpan.Zero, 
            TimeSpan.FromMinutes(_config.CheckIntervalMinutes)
        );

        _logger.LogInformation("加密锁许可证检查服务已启动");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("加密锁许可证检查服务正在停止...");

        IsRunning = false;
        _timer?.Change(Timeout.Infinite, 0);

        _logger.LogInformation("加密锁许可证检查服务已停止");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 执行检查工作
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void DoWork(object? state)
    {
        if (!IsRunning)
        {
            return;
        }

        try
        {
            _logger.LogDebug("开始执行加密锁检查任务...");

            // 执行检查
            var checkResult = await _dongleCheckService.CheckDongleAsync();
            LastCheckResult = checkResult;

            // 根据检查结果记录日志
            if (checkResult.IsSuccess)
            {
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogInformation(
                        "加密锁检查成功 - 设备数量: {Count}, 耗时: {ElapsedMs}ms", 
                        checkResult.DongleCount, 
                        checkResult.ElapsedMilliseconds
                    );
                }
                else
                {
                    _logger.LogDebug("加密锁检查成功");
                }

                // 记录设备详细信息（仅在详细日志模式下）
                if (_config.EnableDetailedLogging && checkResult.DongleInfos.Any())
                {
                    foreach (var device in checkResult.DongleInfos)
                    {
                        _logger.LogDebug(
                            "设备 {Index}: {ProductType}, PID: {ProductId}, 状态: {Status}",
                            device.Index,
                            device.ProductType,
                            device.ProductId,
                            device.CanOpenClose ? "正常" : "异常"
                        );
                    }
                }
            }
            else
            {
                _logger.LogError(
                    "加密锁检查失败 - {ErrorMessage}, 耗时: {ElapsedMs}ms",
                    checkResult.ErrorMessage,
                    checkResult.ElapsedMilliseconds
                );

                // 发送告警（如果配置启用）
                if (_config.SendAlarmOnFailure)
                {
                    await SendAlarmAsync(checkResult);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行加密锁检查任务时发生异常");

            // 创建异常结果
            LastCheckResult = new DongleCheckResult
            {
                IsSuccess = false,
                ErrorMessage = $"检查任务异常: {ex.Message}",
                CheckTime = DateTime.Now
            };

            // 发送告警（如果配置启用）
            if (_config.SendAlarmOnFailure)
            {
                await SendAlarmAsync(LastCheckResult);
            }
        }
    }

    /// <summary>
    /// 发送告警
    /// </summary>
    /// <param name="checkResult">检查结果</param>
    /// <returns>任务</returns>
    private async Task SendAlarmAsync(DongleCheckResult checkResult)
    {
        try
        {
            // 这里可以集成系统的告警机制
            // 例如：发送邮件、短信、推送通知等
            
            _logger.LogWarning(
                "【加密锁告警】检查失败 - 时间: {CheckTime}, 错误: {ErrorMessage}",
                checkResult.CheckTime.ToString("yyyy-MM-dd HH:mm:ss"),
                checkResult.ErrorMessage
            );

            // TODO: 集成具体的告警发送逻辑
            // 可以通过事件总线发送告警事件
            // await _eventBus.PublishAsync(new DongleLicenseAlarmEvent(checkResult));
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送加密锁告警时发生异常");
        }
    }

    /// <summary>
    /// 手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    public async Task<DongleCheckResult> TriggerCheckAsync()
    {
        _logger.LogInformation("手动触发加密锁检查");
        
        try
        {
            var result = await _dongleCheckService.CheckDongleAsync();
            LastCheckResult = result;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发加密锁检查时发生异常");
            
            var errorResult = new DongleCheckResult
            {
                IsSuccess = false,
                ErrorMessage = $"手动检查异常: {ex.Message}",
                CheckTime = DateTime.Now
            };
            
            LastCheckResult = errorResult;
            return errorResult;
        }
    }

    /// <summary>
    /// 获取服务状态信息
    /// </summary>
    /// <returns>状态信息</returns>
    public object GetServiceStatus()
    {
        return new
        {
            IsRunning,
            ConfigEnabled = _config.EnableCheck,
            CheckIntervalMinutes = _config.CheckIntervalMinutes,
            LastCheckTime = LastCheckResult?.CheckTime,
            LastCheckSuccess = LastCheckResult?.IsSuccess,
            LastCheckError = LastCheckResult?.ErrorMessage,
            LastCheckDeviceCount = LastCheckResult?.DongleCount ?? 0
        };
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _timer?.Dispose();
            _disposed = true;
        }
    }
}
