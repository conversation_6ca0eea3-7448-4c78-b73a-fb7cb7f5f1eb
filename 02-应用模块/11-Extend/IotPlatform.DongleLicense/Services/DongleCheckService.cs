using System.Diagnostics;
using IotPlatform.DongleLicense.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using static IotPlatform.DongleLicense.Models.DongleStructures;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁检查服务
/// </summary>
public class DongleCheckService
{
    private readonly DongleApiService _dongleApiService;
    private readonly ILogger<DongleCheckService> _logger;
    private readonly DongleCheckConfig _config;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dongleApiService">加密锁API服务</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">配置选项</param>
    public DongleCheckService(
        DongleApiService dongleApiService,
        ILogger<DongleCheckService> logger,
        IOptions<DongleCheckConfig> config)
    {
        _dongleApiService = dongleApiService;
        _logger = logger;
        _config = config.Value;
    }

    /// <summary>
    /// 执行加密锁检查
    /// </summary>
    /// <returns>检查结果</returns>
    public async Task<DongleCheckResult> CheckDongleAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new DongleCheckResult
        {
            CheckTime = DateTime.Now
        };

        try
        {
            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("开始执行加密锁检查...");
            }

            // 枚举加密锁设备
            var (enumResult, dongleInfo, count) = _dongleApiService.EnumDongle();
            
            if (enumResult != 0)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"枚举加密锁失败，错误代码: {enumResult:X}";
                _logger.LogError("枚举加密锁失败，错误代码: {ErrorCode}", enumResult.ToString("X"));
                return result;
            }

            result.DongleCount = count;
            
            if (count == 0)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "未检测到加密锁设备";
                _logger.LogWarning("未检测到加密锁设备");
                return result;
            }

            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("检测到 {Count} 个加密锁设备", count);
            }

            // 处理每个设备
            for (int i = 0; i < count; i++)
            {
                var deviceInfo = await ProcessDongleDeviceAsync(dongleInfo, i);
                result.DongleInfos.Add(deviceInfo);
            }

            // 检查是否所有设备都正常
            result.IsSuccess = result.DongleInfos.All(d => d.CanOpenClose);
            
            if (!result.IsSuccess)
            {
                var failedDevices = result.DongleInfos.Where(d => !d.CanOpenClose).ToList();
                result.ErrorMessage = $"有 {failedDevices.Count} 个设备无法正常访问";
                
                foreach (var failedDevice in failedDevices)
                {
                    _logger.LogError("设备 {Index} 访问失败: {Error}", 
                        failedDevice.Index, failedDevice.OpenCloseError);
                }
            }
            else
            {
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogInformation("所有加密锁设备检查通过");
                }
            }
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = $"检查过程中发生异常: {ex.Message}";
            _logger.LogError(ex, "加密锁检查过程中发生异常");
        }
        finally
        {
            stopwatch.Stop();
            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("加密锁检查完成，耗时: {ElapsedMs}ms", result.ElapsedMilliseconds);
            }
        }

        return result;
    }

    /// <summary>
    /// 处理单个加密锁设备
    /// </summary>
    /// <param name="dongleInfo">设备信息</param>
    /// <param name="index">设备索引</param>
    /// <returns>设备信息</returns>
    private async Task<DongleDeviceInfo> ProcessDongleDeviceAsync(DONGLE_INFO dongleInfo, int index)
    {
        var deviceInfo = DongleDeviceInfo.FromDongleInfo(dongleInfo, index);

        if (!_config.TestOpenClose)
        {
            deviceInfo.CanOpenClose = true;
            return deviceInfo;
        }

        try
        {
            // 使用超时控制打开关闭操作
            using var cts = new CancellationTokenSource(_config.OpenCloseTimeoutMs);
            
            await Task.Run(() =>
            {
                // 测试打开设备
                var (openResult, handle) = _dongleApiService.OpenDongle(index);
                
                if (openResult != 0)
                {
                    deviceInfo.CanOpenClose = false;
                    deviceInfo.OpenCloseError = $"打开设备失败，错误代码: {openResult:X}";
                    return;
                }

                try
                {
                    // 测试关闭设备
                    var closeResult = _dongleApiService.CloseDongle(handle);
                    
                    if (closeResult != 0)
                    {
                        deviceInfo.CanOpenClose = false;
                        deviceInfo.OpenCloseError = $"关闭设备失败，错误代码: {closeResult:X}";
                        return;
                    }

                    deviceInfo.CanOpenClose = true;
                }
                catch (Exception ex)
                {
                    deviceInfo.CanOpenClose = false;
                    deviceInfo.OpenCloseError = $"关闭设备时发生异常: {ex.Message}";
                    
                    // 尝试强制关闭
                    try
                    {
                        _dongleApiService.CloseDongle(handle);
                    }
                    catch
                    {
                        // 忽略强制关闭时的异常
                    }
                }
            }, cts.Token);
        }
        catch (OperationCanceledException)
        {
            deviceInfo.CanOpenClose = false;
            deviceInfo.OpenCloseError = $"操作超时（{_config.OpenCloseTimeoutMs}ms）";
        }
        catch (Exception ex)
        {
            deviceInfo.CanOpenClose = false;
            deviceInfo.OpenCloseError = $"测试过程中发生异常: {ex.Message}";
        }

        return deviceInfo;
    }

    /// <summary>
    /// 获取加密锁详细信息（用于调试和监控）
    /// </summary>
    /// <returns>详细信息字符串</returns>
    public async Task<string> GetDongleDetailInfoAsync()
    {
        var result = await CheckDongleAsync();
        var info = new System.Text.StringBuilder();
        
        info.AppendLine("=== 加密锁检查详细信息 ===");
        info.AppendLine($"检查时间: {result.CheckTime:yyyy-MM-dd HH:mm:ss}");
        info.AppendLine($"检查结果: {(result.IsSuccess ? "成功" : "失败")}");
        info.AppendLine($"设备数量: {result.DongleCount}");
        info.AppendLine($"检查耗时: {result.ElapsedMilliseconds}ms");
        
        if (!string.IsNullOrEmpty(result.ErrorMessage))
        {
            info.AppendLine($"错误信息: {result.ErrorMessage}");
        }
        
        info.AppendLine();
        
        foreach (var device in result.DongleInfos)
        {
            info.AppendLine($"--- 设备 {device.Index} ---");
            info.AppendLine($"版本: {device.Version}");
            info.AppendLine($"产品类型: {device.ProductType}");
            info.AppendLine($"出厂日期: {device.BirthDay}");
            info.AppendLine($"代理商ID: {device.AgentId}");
            info.AppendLine($"产品ID: {device.ProductId}");
            info.AppendLine($"用户ID: {device.UserId}");
            info.AppendLine($"硬件ID: {device.HardwareId}");
            info.AppendLine($"是否母锁: {(device.IsMother ? "是" : "否")}");
            info.AppendLine($"设备类型: {device.DeviceType}");
            info.AppendLine($"访问状态: {(device.CanOpenClose ? "正常" : "异常")}");
            
            if (!device.CanOpenClose && !string.IsNullOrEmpty(device.OpenCloseError))
            {
                info.AppendLine($"错误信息: {device.OpenCloseError}");
            }
            
            info.AppendLine();
        }
        
        return info.ToString();
    }
}
