using IotPlatform.DongleLicense.HostedService;
using IotPlatform.DongleLicense.Models;
using IotPlatform.DongleLicense.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace IotPlatform.DongleLicense.Controllers;

/// <summary>
/// 加密锁许可证管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DongleLicenseController : ControllerBase
{
    private readonly DongleLicenseHostedService _hostedService;
    private readonly DongleCheckService _checkService;
    private readonly ILogger<DongleLicenseController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="hostedService">后台托管服务</param>
    /// <param name="checkService">检查服务</param>
    /// <param name="logger">日志记录器</param>
    public DongleLicenseController(
        <PERSON>leLicenseHostedService hostedService,
        DongleCheckService checkService,
        ILogger<DongleLicenseController> logger)
    {
        _hostedService = hostedService;
        _checkService = checkService;
        _logger = logger;
    }

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态信息</returns>
    [HttpGet("status")]
    public IActionResult GetServiceStatus()
    {
        try
        {
            var status = _hostedService.GetServiceStatus();
            return Ok(new { success = true, data = status });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态时发生异常");
            return StatusCode(500, new { success = false, message = "获取服务状态失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取最后一次检查结果
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpGet("last-check")]
    public IActionResult GetLastCheckResult()
    {
        try
        {
            var result = _hostedService.LastCheckResult;
            if (result == null)
            {
                return Ok(new { success = true, data = null, message = "尚未执行过检查" });
            }

            return Ok(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最后检查结果时发生异常");
            return StatusCode(500, new { success = false, message = "获取检查结果失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpPost("trigger-check")]
    public async Task<IActionResult> TriggerCheck()
    {
        try
        {
            _logger.LogInformation("通过API手动触发加密锁检查");
            var result = await _hostedService.TriggerCheckAsync();
            
            return Ok(new 
            { 
                success = true, 
                data = result,
                message = result.IsSuccess ? "检查成功" : "检查失败"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发检查时发生异常");
            return StatusCode(500, new { success = false, message = "触发检查失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取加密锁详细信息
    /// </summary>
    /// <returns>详细信息</returns>
    [HttpGet("detail-info")]
    public async Task<IActionResult> GetDetailInfo()
    {
        try
        {
            var detailInfo = await _checkService.GetDongleDetailInfoAsync();
            return Ok(new { success = true, data = detailInfo });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取加密锁详细信息时发生异常");
            return StatusCode(500, new { success = false, message = "获取详细信息失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取加密锁设备列表
    /// </summary>
    /// <returns>设备列表</returns>
    [HttpGet("devices")]
    public async Task<IActionResult> GetDevices()
    {
        try
        {
            var checkResult = await _checkService.CheckDongleAsync();
            
            var devices = checkResult.DongleInfos.Select(d => new
            {
                d.Index,
                d.Version,
                d.ProductType,
                d.BirthDay,
                d.AgentId,
                d.ProductId,
                d.UserId,
                d.HardwareId,
                d.IsMother,
                d.DeviceType,
                d.CanOpenClose,
                d.OpenCloseError,
                Status = d.CanOpenClose ? "正常" : "异常"
            }).ToList();

            return Ok(new 
            { 
                success = true, 
                data = new
                {
                    checkTime = checkResult.CheckTime,
                    isSuccess = checkResult.IsSuccess,
                    deviceCount = checkResult.DongleCount,
                    elapsedMs = checkResult.ElapsedMilliseconds,
                    errorMessage = checkResult.ErrorMessage,
                    devices
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备列表时发生异常");
            return StatusCode(500, new { success = false, message = "获取设备列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取系统健康状态（包含加密锁状态）
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("health")]
    public IActionResult GetHealth()
    {
        try
        {
            var lastResult = _hostedService.LastCheckResult;
            var serviceStatus = _hostedService.GetServiceStatus();
            
            var health = new
            {
                service = new
                {
                    isRunning = _hostedService.IsRunning,
                    status = _hostedService.IsRunning ? "运行中" : "已停止"
                },
                lastCheck = lastResult != null ? new
                {
                    time = lastResult.CheckTime,
                    success = lastResult.IsSuccess,
                    deviceCount = lastResult.DongleCount,
                    message = lastResult.ErrorMessage
                } : null,
                overall = new
                {
                    status = _hostedService.IsRunning && (lastResult?.IsSuccess ?? false) ? "健康" : "异常",
                    timestamp = DateTime.Now
                }
            };

            return Ok(new { success = true, data = health });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取健康状态时发生异常");
            return StatusCode(500, new { success = false, message = "获取健康状态失败", error = ex.Message });
        }
    }
}
