# 加密锁许可证检查模块

## 概述

本模块实现了对加密锁设备的定时检查功能，严格按照提供的WinForm示例代码实现，确保与原有加密锁系统的完全兼容。

## 功能特性

- **定时检查**: 支持配置检查间隔，定时检查加密锁设备状态
- **设备枚举**: 自动枚举所有连接的加密锁设备
- **状态监控**: 检查设备是否能够正常打开和关闭
- **详细信息**: 获取设备的详细信息（版本、产品类型、硬件ID等）
- **告警机制**: 检查失败时可发送告警通知
- **API接口**: 提供REST API用于手动触发检查和获取状态
- **日志记录**: 详细的日志记录，支持不同级别的日志输出

## 文件结构

```
IotPlatform.DongleLicense/
├── Models/
│   ├── DongleStructures.cs          # 加密锁数据结构定义
│   └── DongleCheckResult.cs         # 检查结果模型
├── Services/
│   ├── DongleApiService.cs          # 加密锁API服务
│   └── DongleCheckService.cs        # 加密锁检查服务
├── HostedService/
│   └── DongleLicenseHostedService.cs # 后台托管服务
├── Controllers/
│   └── DongleLicenseController.cs   # API控制器
├── Startup.cs                       # 服务注册配置
├── IotPlatform.DongleLicense.csproj # 项目文件
├── Dongle_d.dll                     # 加密锁动态链接库（需要提供）
└── README.md                        # 说明文档
```

## 配置说明

在 `Configuration/DongleLicense.json` 中配置：

```json
{
  "DongleLicense": {
    "CheckIntervalMinutes": 5,        // 检查间隔（分钟）
    "EnableCheck": true,              // 是否启用检查
    "TestOpenClose": true,            // 是否测试打开关闭操作
    "OpenCloseTimeoutMs": 5000,       // 打开关闭操作超时时间（毫秒）
    "EnableDetailedLogging": true,    // 是否记录详细日志
    "SendAlarmOnFailure": true        // 检查失败时是否发送告警
  }
}
```

## API接口

### 1. 获取服务状态
```
GET /api/DongleLicense/status
```

### 2. 获取最后一次检查结果
```
GET /api/DongleLicense/last-check
```

### 3. 手动触发检查
```
POST /api/DongleLicense/trigger-check
```

### 4. 获取设备列表
```
GET /api/DongleLicense/devices
```

### 5. 获取详细信息
```
GET /api/DongleLicense/detail-info
```

### 6. 获取健康状态
```
GET /api/DongleLicense/health
```

## 部署说明

### 1. 添加DLL文件

请将 `Dongle_d.dll` 文件放置在项目根目录下，该文件会自动复制到输出目录。

### 2. 权限要求

确保应用程序有足够的权限访问USB设备和加密锁硬件。

### 3. 依赖项

- .NET 8.0
- Microsoft.Extensions.Hosting
- Microsoft.Extensions.Logging
- Microsoft.Extensions.Options

## 使用示例

### 手动检查加密锁

```csharp
// 注入服务
private readonly DongleLicenseHostedService _hostedService;

// 手动触发检查
var result = await _hostedService.TriggerCheckAsync();

if (result.IsSuccess)
{
    Console.WriteLine($"检查成功，发现 {result.DongleCount} 个设备");
    foreach (var device in result.DongleInfos)
    {
        Console.WriteLine($"设备 {device.Index}: {device.ProductType}, 状态: {(device.CanOpenClose ? "正常" : "异常")}");
    }
}
else
{
    Console.WriteLine($"检查失败: {result.ErrorMessage}");
}
```

### 获取设备详细信息

```csharp
// 注入服务
private readonly DongleCheckService _checkService;

// 获取详细信息
var detailInfo = await _checkService.GetDongleDetailInfoAsync();
Console.WriteLine(detailInfo);
```

## 日志示例

```
2024-01-20 10:00:00 [Information] 加密锁许可证检查服务正在启动...
2024-01-20 10:00:00 [Information] 检查间隔: 5 分钟
2024-01-20 10:00:00 [Information] 加密锁许可证检查服务已启动
2024-01-20 10:00:01 [Information] 开始执行加密锁检查...
2024-01-20 10:00:01 [Information] 检测到 1 个加密锁设备
2024-01-20 10:00:02 [Information] 加密锁检查成功 - 设备数量: 1, 耗时: 1234ms
2024-01-20 10:00:02 [Debug] 设备 0: 标准版, PID: 12345678, 状态: 正常
```

## 故障排除

### 1. 枚举设备失败
- 检查加密锁是否正确连接
- 确认驱动程序已正确安装
- 检查应用程序权限

### 2. 打开设备失败
- 检查设备是否被其他程序占用
- 确认设备状态正常
- 检查超时设置是否合理

### 3. DLL加载失败
- 确认 `Dongle_d.dll` 文件存在
- 检查DLL文件版本是否匹配
- 确认运行环境（x86/x64）匹配

## 注意事项

1. **线程安全**: 服务实现了线程安全的设备访问
2. **资源管理**: 自动管理设备句柄的打开和关闭
3. **异常处理**: 完善的异常处理机制，避免程序崩溃
4. **性能优化**: 支持超时控制，避免长时间阻塞
5. **兼容性**: 严格按照原WinForm示例实现，确保兼容性

## 版本历史

- v1.0.0: 初始版本，实现基本的加密锁检查功能
