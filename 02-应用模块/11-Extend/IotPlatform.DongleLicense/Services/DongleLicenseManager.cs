using IotPlatform.DongleLicense.Models;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁许可证管理器（单例服务，用于状态管理）
/// </summary>
public class DongleLicenseManager
{
    private readonly DongleCheckConfig _config;
    private readonly ConcurrentDictionary<string, object> _cache = new();

    /// <summary>
    /// 最后一次检查结果
    /// </summary>
    public DongleCheckResult? LastCheckResult { get; set; }

    /// <summary>
    /// 服务是否运行中
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="config">配置选项</param>
    public DongleLicenseManager(IOptions<DongleCheckConfig> config)
    {
        _config = config.Value;
    }

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态信息</returns>
    public dynamic GetServiceStatus()
    {
        return new
        {
            IsRunning,
            ConfigEnabled = _config.EnableCheck,
            CheckIntervalMinutes = _config.CheckIntervalMinutes,
            LastCheckTime = LastCheckResult?.CheckTime,
            LastCheckSuccess = LastCheckResult?.IsSuccess,
            LastCheckError = LastCheckResult?.ErrorMessage,
            LastCheckDeviceCount = LastCheckResult?.DongleCount ?? 0
        };
    }

    /// <summary>
    /// 更新检查结果
    /// </summary>
    /// <param name="result">检查结果</param>
    public void UpdateCheckResult(DongleCheckResult result)
    {
        LastCheckResult = result;
    }

    /// <summary>
    /// 设置服务运行状态
    /// </summary>
    /// <param name="isRunning">是否运行中</param>
    public void SetRunningStatus(bool isRunning)
    {
        IsRunning = isRunning;
    }

    /// <summary>
    /// 获取缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存值</returns>
    public T? GetCacheValue<T>(string key) where T : class
    {
        return _cache.TryGetValue(key, out var value) ? value as T : null;
    }

    /// <summary>
    /// 设置缓存值
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiry">过期时间（可选）</param>
    public void SetCacheValue(string key, object value, TimeSpan? expiry = null)
    {
        _cache.AddOrUpdate(key, value, (k, v) => value);
        
        // 如果设置了过期时间，启动定时清理
        if (expiry.HasValue)
        {
            Task.Delay(expiry.Value).ContinueWith(_ => _cache.TryRemove(key, out _));
        }
    }

    /// <summary>
    /// 清除缓存
    /// </summary>
    /// <param name="key">缓存键，为空则清除所有</param>
    public void ClearCache(string? key = null)
    {
        if (string.IsNullOrEmpty(key))
        {
            _cache.Clear();
        }
        else
        {
            _cache.TryRemove(key, out _);
        }
    }
}
