using IotPlatform.DongleLicense.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using static IotPlatform.DongleLicense.Models.DongleStructures;

namespace IotPlatform.DongleLicense.Controllers;

/// <summary>
/// 加密锁测试控制器（用于调试和测试）
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DongleTestController : ControllerBase
{
    private readonly DongleApiService _dongleApiService;
    private readonly ILogger<DongleTestController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dongleApiService">加密锁API服务</param>
    /// <param name="logger">日志记录器</param>
    public DongleTestController(
        DongleApiService dongleApiService,
        ILogger<DongleTestController> logger)
    {
        _dongleApiService = dongleApiService;
        _logger = logger;
    }

    /// <summary>
    /// 测试加密锁基本功能（类似WinForm示例）
    /// </summary>
    /// <returns>测试结果</returns>
    [HttpPost("basic-test")]
    public IActionResult BasicTest()
    {
        var result = new StringBuilder();
        
        try
        {
            _logger.LogInformation("开始执行加密锁基本功能测试");
            
            // 1. 枚举加密锁
            result.AppendLine("=== 加密锁基本功能测试 ===");
            
            var (enumResult, pDongleInfo, pCount) = _dongleApiService.EnumDongle();
            
            if (enumResult != 0)
            {
                result.AppendLine($"Enum Dongle Failed! Return value: {enumResult:X}");
                return Ok(new { success = false, message = "枚举加密锁失败", data = result.ToString() });
            }
            
            result.AppendLine($"Enum Dongle Success! Count: {pCount}");
            
            // 2. 获取设备信息
            var (getInfoResult, dongleInfo, count) = _dongleApiService.EnumDongle();
            if (getInfoResult != 0)
            {
                result.AppendLine($"GetInfo Dongle Failed! Return value: {getInfoResult:X}");
                return Ok(new { success = false, message = "获取设备信息失败", data = result.ToString() });
            }
            
            result.AppendLine("GetInfo Dongle Success!");
            
            // 3. 显示设备详细信息
            for (int k = 0; k < pCount; k++)
            {
                result.AppendLine($"\n*********Dongle ARM INFO*******");
                result.AppendLine($"The index: {k}");
                result.AppendLine($"Agent ID: {pDongleInfo.m_Agent:X}");
                result.AppendLine($"Dev Type: {pDongleInfo.m_DevType}");
                result.Append("HID: ");
                
                if (pDongleInfo.m_HID != null)
                {
                    for (int i = 0; i < 8; i++)
                    {
                        result.Append($"{pDongleInfo.m_HID[i]:X2} ");
                    }
                }
                result.AppendLine();
                
                if (pDongleInfo.m_BirthDay != null && pDongleInfo.m_BirthDay.Length >= 6)
                {
                    result.AppendLine($"Birth day: 20{pDongleInfo.m_BirthDay[0]:X2}-{pDongleInfo.m_BirthDay[1]:X2}-{pDongleInfo.m_BirthDay[2]:X2} {pDongleInfo.m_BirthDay[3]:X2}:{pDongleInfo.m_BirthDay[4]:X2}:{pDongleInfo.m_BirthDay[5]:X2}");
                }
                
                result.AppendLine($"Is Mother Dongle: {pDongleInfo.m_IsMother}");
                result.AppendLine($"PID: {pDongleInfo.m_PID:X}");
                result.AppendLine($"Product Type: {pDongleInfo.m_Type:X}");
                result.AppendLine($"UID: {pDongleInfo.m_UserID:X}");
            }
            
            // 4. 测试打开和关闭设备
            var (openResult, hDongle) = _dongleApiService.OpenDongle(0);
            if (openResult != 0)
            {
                result.AppendLine($"Open Dongle Failed! Return value: {openResult:X}");
                return Ok(new { success = false, message = "打开设备失败", data = result.ToString() });
            }
            
            result.AppendLine("Open Dongle Success!");
            
            // 5. 关闭设备
            var closeResult = _dongleApiService.CloseDongle(hDongle);
            if (closeResult != 0)
            {
                result.AppendLine($"Close Dongle Failed! Return value: {closeResult:X}");
                return Ok(new { success = false, message = "关闭设备失败", data = result.ToString() });
            }
            
            result.AppendLine("Close Dongle Success!");
            
            _logger.LogInformation("加密锁基本功能测试完成");
            
            return Ok(new 
            { 
                success = true, 
                message = "测试完成", 
                data = result.ToString(),
                deviceCount = pCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行加密锁基本功能测试时发生异常");
            result.AppendLine($"Exception: {ex.Message}");
            
            return StatusCode(500, new 
            { 
                success = false, 
                message = "测试过程中发生异常", 
                error = ex.Message,
                data = result.ToString()
            });
        }
    }

    /// <summary>
    /// 测试设备枚举
    /// </summary>
    /// <returns>枚举结果</returns>
    [HttpGet("enum-test")]
    public IActionResult EnumTest()
    {
        try
        {
            _logger.LogInformation("开始执行设备枚举测试");
            
            var (result, dongleInfo, count) = _dongleApiService.EnumDongle();
            
            if (result != 0)
            {
                return Ok(new 
                { 
                    success = false, 
                    message = "枚举失败", 
                    errorCode = result.ToString("X"),
                    count = 0
                });
            }
            
            var deviceInfo = new
            {
                version = dongleInfo.m_Ver.ToString("X4"),
                type = dongleInfo.m_Type.ToString("X"),
                agentId = dongleInfo.m_Agent.ToString("X"),
                productId = dongleInfo.m_PID.ToString("X"),
                userId = dongleInfo.m_UserID.ToString("X"),
                isMother = dongleInfo.m_IsMother == 1,
                deviceType = dongleInfo.m_DevType,
                hardwareId = dongleInfo.m_HID != null ? 
                    string.Join(" ", dongleInfo.m_HID.Take(8).Select(b => b.ToString("X2"))) : "N/A",
                birthDay = dongleInfo.m_BirthDay != null && dongleInfo.m_BirthDay.Length >= 6 ?
                    $"20{dongleInfo.m_BirthDay[0]:X2}-{dongleInfo.m_BirthDay[1]:X2}-{dongleInfo.m_BirthDay[2]:X2} {dongleInfo.m_BirthDay[3]:X2}:{dongleInfo.m_BirthDay[4]:X2}:{dongleInfo.m_BirthDay[5]:X2}" : "N/A"
            };
            
            return Ok(new 
            { 
                success = true, 
                message = "枚举成功", 
                count,
                deviceInfo
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行设备枚举测试时发生异常");
            return StatusCode(500, new { success = false, message = "枚举测试异常", error = ex.Message });
        }
    }

    /// <summary>
    /// 测试设备打开关闭
    /// </summary>
    /// <param name="index">设备索引</param>
    /// <returns>测试结果</returns>
    [HttpPost("open-close-test")]
    public IActionResult OpenCloseTest([FromQuery] int index = 0)
    {
        try
        {
            _logger.LogInformation("开始执行设备打开关闭测试，设备索引: {Index}", index);
            
            // 打开设备
            var (openResult, handle) = _dongleApiService.OpenDongle(index);
            if (openResult != 0)
            {
                return Ok(new 
                { 
                    success = false, 
                    message = "打开设备失败", 
                    errorCode = openResult.ToString("X"),
                    step = "open"
                });
            }
            
            // 关闭设备
            var closeResult = _dongleApiService.CloseDongle(handle);
            if (closeResult != 0)
            {
                return Ok(new 
                { 
                    success = false, 
                    message = "关闭设备失败", 
                    errorCode = closeResult.ToString("X"),
                    step = "close"
                });
            }
            
            return Ok(new 
            { 
                success = true, 
                message = "设备打开关闭测试成功",
                handle = handle.ToString("X")
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行设备打开关闭测试时发生异常");
            return StatusCode(500, new { success = false, message = "打开关闭测试异常", error = ex.Message });
        }
    }
}
