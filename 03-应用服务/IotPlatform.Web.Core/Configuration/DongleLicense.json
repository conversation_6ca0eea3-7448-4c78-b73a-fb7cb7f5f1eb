{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "DongleLicense": {
    // 检查间隔（分钟）
    "CheckIntervalMinutes": 5,
    // 是否启用检查
    "EnableCheck": true,
    // 是否测试打开关闭操作
    "TestOpenClose": true,
    // 打开关闭操作超时时间（毫秒）
    "OpenCloseTimeoutMs": 5000,
    // 是否记录详细日志
    "EnableDetailedLogging": true,
    // 检查失败时是否发送告警
    "SendAlarmOnFailure": true
  }
}
