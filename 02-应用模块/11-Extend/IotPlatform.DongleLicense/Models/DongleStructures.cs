using System.Runtime.InteropServices;

namespace IotPlatform.DongleLicense.Models;

/// <summary>
/// 加密锁相关数据结构定义
/// </summary>
public static class DongleStructures
{
    /************************************************************************/
    /*                              结构                                    */
    /************************************************************************/
    
    /// <summary>
    /// RSA公钥格式(兼容1024,2048)
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct RSA_PUBLIC_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        public uint modulus;				  // modulus
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] exponent;       // public exponent
    }

    /// <summary>
    /// RSA私钥格式(兼容1024,2048)
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct RSA_PRIVATE_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        public uint modulus;				  // modulus  
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] publicExponent;       // public exponent
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] exponent;       // public exponent
    }

    /// <summary>
    /// 外部ECCSM2公钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)公钥格式
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct ECCSM2_PUBLIC_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] XCoordinate;       // 曲线上点的X坐标
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] YCoordinate;       // 曲线上点的Y坐标
    }

    /// <summary>
    /// 外部ECCSM2私钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)私钥格式  
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct ECCSM2_PRIVATE_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] PrivateKey;           // 私钥
    }

    /// <summary>
    /// 加密锁信息
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct DONGLE_INFO
    {
        public ushort m_Ver;               //COS版本,比如:0x0201,表示2.01版             	
        public ushort m_Type;              //产品类型: 0xFF表示标准版, 0x00为时钟锁,0x01为带时钟的U盘锁,0x02为标准U盘锁  
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] m_BirthDay;       //出厂日期 
        public uint m_Agent;             //代理商编号,比如:默认的0xFFFFFFFF
        public uint m_PID;               //产品ID
        public uint m_UserID;            //用户ID
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] m_HID;            //8字节的硬件ID
        public uint m_IsMother;          //母锁标志: 0x01表示是母锁, 0x00表示不是母锁
        public uint m_DevType;           //设备类型(PROTOCOL_HID或者PROTOCOL_CCID)
    }

    /*************************文件授权结构***********************************/
    
    /// <summary>
    /// 数据文件授权结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_LIC
    {
        public ushort m_Read_Priv;     //读权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限            	
        public ushort m_Write_Priv;    //写权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    /// <summary>
    /// 私钥文件授权结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_LIC
    {
        public uint m_Count;        //可调次数: 0xFFFFFFFF表示不限制, 递减到0表示已不可调用
        public byte m_Priv;         //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
        public byte m_IsDecOnRAM;   //是否是在内存中递减: 1为在内存中递减，0为在FLASH中递减
        public byte m_IsReset;      //用户态调用后是否自动回到匿名态: TRUE为调后回到匿名态 (开发商态不受此限制)
        public byte m_Reserve;      //保留,用于4字节对齐
    }

    /// <summary>
    /// 对称加密算法(SM4/TDES)密钥文件授权结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_LIC
    {
        public uint m_Priv_Enc;   //加密时的调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    /// <summary>
    /// 可执行文件授权结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_LIC
    {
        public ushort m_Priv_Exe;   //运行的权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    /****************************文件属性结构********************************/
    
    /// <summary>
    /// 数据文件属性数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_FILE_ATTR
    {
        public uint m_Size;      //数据文件长度，该值最大为4096
        public DATA_LIC m_Lic;       //授权
    }

    /// <summary>
    /// ECCSM2/RSA私钥文件属性数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_FILE_ATTR
    {
        public ushort m_Type;       //数据类型:ECCSM2私钥 或 RSA私钥
        public ushort m_Size;       //数据长度:RSA该值为1024或2048, ECC该值为192或256, SM2该值为0x8100
        public PRIKEY_LIC m_Lic;        //授权
    }

    /// <summary>
    /// 对称加密算法(SM4/TDES)密钥文件属性数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_FILE_ATTR
    {
        public uint m_Size;       //密钥数据长度=16
        public KEY_LIC m_Lic;        //授权
    }

    /// <summary>
    /// 可执行文件属性数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_ATTR
    {
        public EXE_LIC m_Lic;        //授权	
        public ushort m_Len;        //文件长度
    }

    /*************************文件列表结构***********************************/
    
    /// <summary>
    /// 获取私钥文件列表时返回的数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public PRIKEY_FILE_ATTR m_attr;    //文件属性
    }

    /// <summary>
    /// 获取SM4及TDES密钥文件列表时返回的数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public KEY_FILE_ATTR m_attr;    //文件属性
    }

    /// <summary>
    /// 获取数据文件列表时返回的数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public DATA_FILE_ATTR m_attr;    //文件属性
    }

    /// <summary>
    /// 获取可执行文件列表时返回的数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_LIST
    {
        public ushort m_FILEID;    //文件ID
        public EXE_FILE_ATTR m_attr;
        public ushort m_Reserve;  //保留,用于4字节对齐
    }

    /// <summary>
    /// 下载和列可执行文件时填充的数据结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_INFO
    {
        public ushort m_dwSize;           //可执行文件大小
        public ushort m_wFileID;          //可执行文件ID
        public byte m_Priv;             //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
        public byte[] m_pData;            //可执行文件数据
    }

    /// <summary>
    /// 需要发给空锁的初始化数据
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct SON_DATA
    {
        public int m_SeedLen;                 //种子码长度
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string m_SeedForPID;	       //产生产品ID和开发商密码的种子码 (最长250个字节)
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 18)]
        public string m_UserPIN;         //用户密码(16个字符的0终止字符串)
        public sbyte m_UserTryCount;            //用户密码允许的最大错误重试次数
        public sbyte m_AdminTryCount;           //开发商密码允许的最大错误重试次数
        public int m_UserID_Start;            //起始用户ID
    }

    /// <summary>
    /// 母锁数据
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct MOTHER_DATA
    {
        public SON_DATA m_Son;                  //子锁初始化数据
        public int m_Count;                //可产生子锁初始化数据的次数 (-1表示不限制次数, 递减到0时会受限)
    }
}
